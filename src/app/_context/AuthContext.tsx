'use client';

import {
  logoutUser,
  sendPhoneVerificationDirect,
  verifyOtpCodeDirect,
} from '@/src/app/_actions/auth';
import Cookies from 'js-cookie';
import { createContext, useContext, useEffect, useState } from 'react';

// Define the shape of the authentication state
interface AuthState {
  isAuthenticated: boolean;
  phoneNumber: string | null;
  token: string | null;
  userId: string | null;
}

// Define the shape of the context
interface AuthContextType {
  auth: AuthState;
  setAuth: (auth: Partial<AuthState>) => void;
  login: (token: string, userId: string, phoneNumber: string) => void;
  logout: () => Promise<void>;
  startPhoneVerification: (phoneNumber: string) => Promise<boolean>;
  verifyOtp: (phoneNumber: string, otp: string) => Promise<boolean>;
}

// Create the context with a default value
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Auth token cookie name
const AUTH_TOKEN_COOKIE = 'auth_token';
const USER_ID_COOKIE = 'user_id';
const PHONE_NUMBER_COOKIE = 'phone_number';

// Provider component
export function AuthProvider({ children }: { children: React.ReactNode }) {
  // Initialize auth state
  const [auth, setAuthState] = useState<AuthState>({
    isAuthenticated: false,
    phoneNumber: null,
    token: null,
    userId: null,
  });

  // Load auth state from cookies on mount
  useEffect(() => {
    const token = Cookies.get(AUTH_TOKEN_COOKIE);
    const userId = Cookies.get(USER_ID_COOKIE);
    const phoneNumber = Cookies.get(PHONE_NUMBER_COOKIE);

    if (token && userId) {
      setAuthState({
        isAuthenticated: true,
        token,
        userId,
        phoneNumber: phoneNumber || null,
      });
    }
  }, []);

  // Update auth state
  const setAuth = (newAuth: Partial<AuthState>) => {
    setAuthState((prev) => ({ ...prev, ...newAuth }));
  };

  // Login function
  const login = (token: string, userId: string, phoneNumber: string) => {
    // Set cookies
    Cookies.set(AUTH_TOKEN_COOKIE, token, { expires: 7, secure: true, sameSite: 'strict' });
    Cookies.set(USER_ID_COOKIE, userId, { expires: 7, secure: true, sameSite: 'strict' });
    Cookies.set(PHONE_NUMBER_COOKIE, phoneNumber, { expires: 7, secure: true, sameSite: 'strict' });

    // Update state
    setAuthState({
      isAuthenticated: true,
      token,
      userId,
      phoneNumber,
    });
  };

  // Logout function
  const logout = async () => {
    try {
      // Clear server-side cookies
      await logoutUser();
    } catch (error) {
      console.error('Error during logout:', error);
    }

    // Remove client-side cookies as fallback
    Cookies.remove(AUTH_TOKEN_COOKIE);
    Cookies.remove(USER_ID_COOKIE);
    Cookies.remove(PHONE_NUMBER_COOKIE);

    // Update state
    setAuthState({
      isAuthenticated: false,
      token: null,
      userId: null,
      phoneNumber: null,
    });
  };

  // Start phone verification
  const startPhoneVerification = async (phoneNumber: string): Promise<boolean> => {
    try {
      const result = await sendPhoneVerificationDirect(phoneNumber);

      if (result.success) {
        // Update state with phone number
        setAuth({ phoneNumber });
        return true;
      } else {
        console.error('Phone verification failed:', result.error);
        return false;
      }
    } catch (error) {
      console.error('Error starting phone verification:', error);
      return false;
    }
  };

  // Verify OTP
  const verifyOtp = async (phoneNumber: string, otp: string): Promise<boolean> => {
    try {
      const result = await verifyOtpCodeDirect(phoneNumber, otp);

      if (result.success && result.data?.token && result.data?.userId) {
        // Login with the received token
        login(result.data.token, result.data.userId, phoneNumber);
        return true;
      } else {
        console.error('OTP verification failed:', result.error);
        return false;
      }
    } catch (error) {
      console.error('Error verifying OTP:', error);
      return false;
    }
  };

  // Provide the context value
  const contextValue: AuthContextType = {
    auth,
    setAuth,
    login,
    logout,
    startPhoneVerification,
    verifyOtp,
  };

  return <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>;
}

// Custom hook to use the auth context
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
