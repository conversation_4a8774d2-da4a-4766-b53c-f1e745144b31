'use client';

import Cookies from 'js-cookie';
import { createContext, useContext, useEffect, useState } from 'react';

// Define the shape of the authentication state
interface AuthState {
  isAuthenticated: boolean;
  phoneNumber: string | null;
  token: string | null;
  userId: string | null;
}

// Define the shape of the context
interface AuthContextType {
  auth: AuthState;
  setAuth: (auth: Partial<AuthState>) => void;
  login: (token: string, userId: string, phoneNumber: string) => void;
  logout: () => void;
  startPhoneVerification: (phoneNumber: string) => Promise<boolean>;
  verifyOtp: (phoneNumber: string, otp: string) => Promise<boolean>;
}

// Create the context with a default value
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Auth token cookie name
const AUTH_TOKEN_COOKIE = 'auth_token';
const USER_ID_COOKIE = 'user_id';
const PHONE_NUMBER_COOKIE = 'phone_number';

// Provider component
export function AuthProvider({ children }: { children: React.ReactNode }) {
  // Initialize auth state
  const [auth, setAuthState] = useState<AuthState>({
    isAuthenticated: false,
    phoneNumber: null,
    token: null,
    userId: null,
  });

  // Load auth state from cookies on mount
  useEffect(() => {
    const token = Cookies.get(AUTH_TOKEN_COOKIE);
    const userId = Cookies.get(USER_ID_COOKIE);
    const phoneNumber = Cookies.get(PHONE_NUMBER_COOKIE);

    if (token && userId) {
      setAuthState({
        isAuthenticated: true,
        token,
        userId,
        phoneNumber: phoneNumber || null,
      });
    }
  }, []);

  // Update auth state
  const setAuth = (newAuth: Partial<AuthState>) => {
    setAuthState((prev) => ({ ...prev, ...newAuth }));
  };

  // Login function
  const login = (token: string, userId: string, phoneNumber: string) => {
    // Set cookies
    Cookies.set(AUTH_TOKEN_COOKIE, token, { expires: 7, secure: true, sameSite: 'strict' });
    Cookies.set(USER_ID_COOKIE, userId, { expires: 7, secure: true, sameSite: 'strict' });
    Cookies.set(PHONE_NUMBER_COOKIE, phoneNumber, { expires: 7, secure: true, sameSite: 'strict' });

    // Update state
    setAuthState({
      isAuthenticated: true,
      token,
      userId,
      phoneNumber,
    });
  };

  // Logout function
  const logout = () => {
    // Remove cookies
    Cookies.remove(AUTH_TOKEN_COOKIE);
    Cookies.remove(USER_ID_COOKIE);
    Cookies.remove(PHONE_NUMBER_COOKIE);

    // Update state
    setAuthState({
      isAuthenticated: false,
      token: null,
      userId: null,
      phoneNumber: null,
    });
  };

  // Start phone verification
  const startPhoneVerification = async (phoneNumber: string): Promise<boolean> => {
    try {
      // Simulate API delay to show loading state
      await new Promise((resolve) => setTimeout(resolve, 2500));

      // This would be a real API call in production
      // For now, we'll simulate a successful response
      // const response = await fetch('/api/auth/verify-phone', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({ phoneNumber }),
      // });

      // if (!response.ok) throw new Error('Failed to send verification code');

      // Update state with phone number
      setAuth({ phoneNumber });
      return true;
    } catch (error) {
      console.error('Error starting phone verification:', error);
      return false;
    }
  };

  // Verify OTP
  const verifyOtp = async (phoneNumber: string, otp: string): Promise<boolean> => {
    try {
      // Simulate API delay to show loading state
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // This would be a real API call in production
      // For now, we'll simulate a successful response
      // const response = await fetch('/api/auth/verify-otp', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({ phoneNumber, otp }),
      // });

      // if (!response.ok) throw new Error('Invalid verification code');
      // const data = await response.json();

      // Mock successful response
      const mockResponse = {
        token: 'mock-jwt-token',
        userId: 'user-123',
      };

      // Login with the received token
      login(mockResponse.token, mockResponse.userId, phoneNumber);
      return true;
    } catch (error) {
      console.error('Error verifying OTP:', error);
      return false;
    }
  };

  // Provide the context value
  const contextValue: AuthContextType = {
    auth,
    setAuth,
    login,
    logout,
    startPhoneVerification,
    verifyOtp,
  };

  return <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>;
}

// Custom hook to use the auth context
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
