/**
 * Authentication-related TypeScript interfaces and types
 */

// API Request/Response Types
export interface PhoneVerificationRequest {
  phoneCode: string; // DDD (e.g., "11")
  phoneNumber: string; // Phone number without country code
}

export interface PhoneVerificationResponse {
  success: boolean;
  message?: string;
  error?: string;
}

export interface OtpConfirmationRequest {
  phoneCode: string; // Same DDD code from verification step
  phoneNumber: string; // Same phone number from verification step
  token: string; // 6-digit OTP code
}

export interface OtpConfirmationResponse {
  success: boolean;
  token?: string; // JWT token for authentication
  userId?: string; // User ID
  message?: string;
  error?: string;
}

// Error Types
export interface AuthError {
  type: 'NETWORK_ERROR' | 'INVALID_PHONE' | 'INVALID_OTP' | 'SERVER_ERROR' | 'UNKNOWN_ERROR';
  message: string;
  details?: string;
}

// Phone Number Processing Types
export interface PhoneNumberParts {
  phoneCode: string; // DDD
  phoneNumber: string; // Remaining digits
  fullNumber: string; // Complete number with formatting
}

// Server Action Result Types
export interface ServerActionResult<T = unknown> {
  success: boolean;
  data?: T;
  error?: AuthError;
}

export type PhoneVerificationResult = ServerActionResult<PhoneVerificationResponse>;
export type OtpConfirmationResult = ServerActionResult<OtpConfirmationResponse>;

// Form State Types
export interface PhoneVerificationState {
  isLoading: boolean;
  error: AuthError | null;
  phoneNumber: string | null;
  isVerificationSent: boolean;
}

export interface OtpVerificationState {
  isLoading: boolean;
  error: AuthError | null;
  isVerifying: boolean;
}
