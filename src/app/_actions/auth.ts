'use server';

import type {
  AuthError,
  OtpConfirmationRequest,
  OtpConfirmationResponse,
  OtpConfirmationResult,
  PhoneVerificationRequest,
  PhoneVerificationResponse,
  PhoneVerificationResult,
} from '@/src/app/_interfaces';
import { splitBrazilianPhoneNumber } from '@/src/app/_utils/validation/phoneValidation';
import { cookies } from 'next/headers';

const API_BASE_URL = process.env.NEXT_PRIVATE_API_BASE_URL;

if (!API_BASE_URL) {
  throw new Error('NEXT_PRIVATE_API_BASE_URL environment variable is not set');
}

/**
 * Creates an AuthError object with proper typing
 */
function createAuthError(type: AuthError['type'], message: string, details?: string): AuthError {
  return { type, message, details };
}

/**
 * Server Action: Send phone verification code
 * Calls POST /api/v1/authenticator to validate phone and send OTP
 */
export async function sendPhoneVerification(phoneNumber: string): Promise<PhoneVerificationResult> {
  try {
    // Validate and split the phone number
    const phoneNumberParts = splitBrazilianPhoneNumber(phoneNumber);

    const requestPayload: PhoneVerificationRequest = {
      phoneCode: phoneNumberParts.phoneCode,
      phoneNumber: phoneNumberParts.phoneNumber,
    };

    const response = await fetch(`${API_BASE_URL}/authenticator`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'service-provider': 'EUR',
      },
      body: JSON.stringify(requestPayload),
    });

    if (!response.ok) {
      const errorText = await response.text();
      let errorMessage = 'Erro ao enviar código de verificação';

      if (response.status === 400) {
        errorMessage = 'Número de telefone inválido';
      } else if (response.status === 404) {
        errorMessage = 'Número de telefone não encontrado';
      } else if (response.status >= 500) {
        errorMessage = 'Erro interno do servidor. Tente novamente.';
      }

      return {
        success: false,
        error: createAuthError(
          response.status === 400 ? 'INVALID_PHONE' : 'SERVER_ERROR',
          errorMessage,
          errorText
        ),
      };
    }

    const data: PhoneVerificationResponse = await response.json();

    return {
      success: true,
      data,
    };
  } catch (error) {
    console.error('Error in sendPhoneVerification:', error);

    if (error instanceof Error && error.message.includes('Invalid Brazilian')) {
      return {
        success: false,
        error: createAuthError(
          'INVALID_PHONE',
          'Número de telefone inválido. Verifique o DDD e tente novamente.',
          error.message
        ),
      };
    }

    return {
      success: false,
      error: createAuthError(
        'NETWORK_ERROR',
        'Erro de conexão. Verifique sua internet e tente novamente.',
        error instanceof Error ? error.message : 'Unknown error'
      ),
    };
  }
}

/**
 * Server Action: Verify OTP code
 * Calls POST /api/v1/authenticator/confirm to validate OTP and complete authentication
 */
export async function verifyOtpCode(
  phoneNumber: string,
  otpCode: string
): Promise<OtpConfirmationResult> {
  try {
    // Validate and split the phone number
    const phoneNumberParts = splitBrazilianPhoneNumber(phoneNumber);

    const requestPayload: OtpConfirmationRequest = {
      phoneCode: phoneNumberParts.phoneCode,
      phoneNumber: phoneNumberParts.phoneNumber,
      token: otpCode,
    };

    const response = await fetch(`${API_BASE_URL}/authenticator/confirm`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'service-provider': 'EUR',
      },
      body: JSON.stringify(requestPayload),
    });

    if (!response.ok) {
      const errorText = await response.text();
      let errorMessage = 'Código de verificação inválido';

      if (response.status === 400) {
        errorMessage = 'Código de verificação inválido ou expirado';
      } else if (response.status === 404) {
        errorMessage = 'Sessão de verificação não encontrada';
      } else if (response.status >= 500) {
        errorMessage = 'Erro interno do servidor. Tente novamente.';
      }

      return {
        success: false,
        error: createAuthError(
          response.status === 400 ? 'INVALID_OTP' : 'SERVER_ERROR',
          errorMessage,
          errorText
        ),
      };
    }

    const data: OtpConfirmationResponse = await response.json();

    // If authentication is successful, set HTTP-only cookies
    if (data.success && data.token && data.userId) {
      const cookieStore = await cookies();

      // Set secure HTTP-only cookies
      cookieStore.set('token', data.token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 7 * 24 * 60 * 60, // 7 days
        path: '/',
      });

      cookieStore.set('userId', data.userId, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 7 * 24 * 60 * 60, // 7 days
        path: '/',
      });

      cookieStore.set('phoneNumber', phoneNumberParts.fullNumber, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 7 * 24 * 60 * 60, // 7 days
        path: '/',
      });
    }

    return {
      success: true,
      data,
    };
  } catch (error) {
    console.error('Error in verifyOtpCode:', error);

    if (error instanceof Error && error.message.includes('Invalid Brazilian')) {
      return {
        success: false,
        error: createAuthError(
          'INVALID_PHONE',
          'Número de telefone inválido. Verifique o DDD e tente novamente.',
          error.message
        ),
      };
    }

    return {
      success: false,
      error: createAuthError(
        'NETWORK_ERROR',
        'Erro de conexão. Verifique sua internet e tente novamente.',
        error instanceof Error ? error.message : 'Unknown error'
      ),
    };
  }
}

/**
 * Server Action: Logout user
 * Clears authentication cookies
 */
export async function logoutUser(): Promise<void> {
  const cookieStore = await cookies();

  // Clear all authentication cookies
  cookieStore.delete('token');
  cookieStore.delete('userId');
  cookieStore.delete('phoneNumber');
}
