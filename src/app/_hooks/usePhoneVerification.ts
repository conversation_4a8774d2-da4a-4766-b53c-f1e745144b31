'use client';

import { useAuth } from '@/src/app/_context/AuthContext';
import { validatePhoneNumber } from '@/src/app/_utils/validation/phoneValidation';
import { useState } from 'react';

export function usePhoneVerification() {
  const { startPhoneVerification, verifyOtp } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isVerificationSent, setIsVerificationSent] = useState(false);
  const [phoneNumber, setPhoneNumber] = useState('');

  // Send verification code to phone number
  const sendVerificationCode = async (phone: string) => {
    setIsLoading(true);
    setError(null);

    try {
      // Use the centralized validation utility
      const { isValid, error: validationError, formattedPhone } = validatePhoneNumber(phone, '+55');

      if (!isValid) {
        throw new Error(validationError || 'Número de telefone inválido');
      }

      // Extract digits for API call
      const digits = phone.replace(/\D/g, '');

      // Call the API to send verification code
      const success = await startPhoneVerification(digits);

      if (success) {
        setPhoneNumber(digits);
        setIsVerificationSent(true);
      } else {
        throw new Error('Falha ao enviar o código de verificação');
      }

      return success;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro ao enviar código de verificação');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Verify OTP code
  const verifyCode = async (otp: string) => {
    setIsLoading(true);
    setError(null);

    try {
      // Validate OTP (basic validation)
      if (otp.length !== 6 || !/^\d+$/.test(otp)) {
        throw new Error('Código de verificação inválido');
      }

      // Call the API to verify OTP
      const success = await verifyOtp(phoneNumber, otp);

      if (!success) {
        throw new Error('Código de verificação inválido');
      }

      return success;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro ao verificar código');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Reset the verification state
  const resetVerification = () => {
    setIsVerificationSent(false);
    setPhoneNumber('');
    setError(null);
  };

  return {
    isLoading,
    error,
    isVerificationSent,
    phoneNumber,
    sendVerificationCode,
    verifyCode,
    resetVerification,
  };
}
